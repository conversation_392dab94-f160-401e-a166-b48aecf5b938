# 闲鱼自动代理配置文件示例
# 复制此文件为 .env 并填入实际配置值

# ===== AI配置 =====
# OpenAI API密钥（必填）- 默认使用通义千问,apikey通过百炼模型平台获取
API_KEY=your_api_key_here

# AI模型配置
MODEL_BASE_URL=https://dashscope.aliyuncs.com/compatible-mode/v1
MODEL_NAME=qwen-max
AI_TEMPERATURE=0.4
AI_MAX_TOKENS=500
AI_TOP_P=0.8

# ===== 安全配置 =====
# 闲鱼Cookie字符串（必填）
COOKIES_STR=your_cookies_here

# 人工接管关键词
TOGGLE_KEYWORDS=。

# 安全配置
MANUAL_MODE_TIMEOUT=3600
MAX_LOGIN_ATTEMPTS=3
SESSION_TIMEOUT=7200

# ===== WebSocket配置 =====
WEBSOCKET_URL=wss://wss-goofish.dingtalk.com/
HEARTBEAT_INTERVAL=15
HEARTBEAT_TIMEOUT=5
TOKEN_REFRESH_INTERVAL=3600
TOKEN_RETRY_INTERVAL=300
MESSAGE_EXPIRE_TIME=300000

# ===== 数据库配置 =====
DB_PATH=data/chat_history.db
MAX_HISTORY=100
BACKUP_INTERVAL=3600

# ===== 日志配置 =====
LOG_LEVEL=INFO
LOG_FILE_PATH=logs/xianyu_agent.log
LOG_ROTATION=100 MB
LOG_RETENTION=30 days