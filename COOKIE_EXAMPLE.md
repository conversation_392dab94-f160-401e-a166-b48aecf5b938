# 🍪 闲鱼Cookie获取和格式说明

## 📋 Cookie示例格式

正确的闲鱼Cookie应该包含以下必要字段：

```
unb=1234567890; _tb_token_=abcdef123456; cookie2=1a2b3c4d5e6f; _samesite_flag_=true; t=1234567890abcdef; _m_h5_tk=abcdef_1234567890; _m_h5_tk_enc=1234567890abcdef; xlly_s=1; isg=BIGabcdef123456; l=FBabcdef123456; tfstk=abcdef123456; sgcookie=E100abcdef123456; uc3=vt3=F8dabcdef123456; csg=abcdef123456; uc4=nk4=0%40abcdef123456; tracknick=abcdef123456; lgc=abcdef123456; dnk=abcdef123456; skt=abcdef123456; existShop=MTYabcdef123456; uc1=cookie16=abcdef123456; publishItemObj=Fabcdef123456; _cc_=abcdef123456; enc=abcdef123456; hng=abcdef123456; mt=ci=abcdef123456; swfstore=abcdef123456; v=0; _nk_=abcdef123456; cookie17=abcdef123456; cookie1=abcdef123456; login=true; cookie21=abcdef123456; sg=abcdef123456; _l_g_=abcdef123456; unb=1234567890; cna=abcdef123456; munb=1234567890; _umdata=abcdef123456; JSESSIONID=abcdef123456; alitrackid=abcdef123456; lastalitrackid=abcdef123456
```

## 🔍 必要字段说明

### 1. unb (用户标识)
- **格式**: `unb=数字`
- **示例**: `unb=1234567890`
- **说明**: 用户的唯一标识符，通常是10位以上的数字

### 2. _tb_token_ (认证令牌)
- **格式**: `_tb_token_=字母数字组合`
- **示例**: `_tb_token_=abcdef123456`
- **说明**: 淘宝认证令牌，用于API调用验证

### 3. cookie2 (会话信息)
- **格式**: `cookie2=字母数字组合`
- **示例**: `cookie2=1a2b3c4d5e6f`
- **说明**: 会话标识符，维持登录状态

## 📖 获取步骤详解

### 步骤1: 准备工作
1. 使用Chrome、Edge或Firefox浏览器
2. 确保浏览器版本较新
3. 清除浏览器缓存（可选，但推荐）

### 步骤2: 登录闲鱼
1. 访问 https://www.xianyu.com
2. 点击登录，使用淘宝/支付宝账号
3. 确保完全登录成功，能看到个人信息

### 步骤3: 打开开发者工具
1. 按 `F12` 键，或右键选择"检查元素"
2. 切换到 `Network` (网络) 标签页
3. 如果已有请求记录，点击清空按钮 🗑️

### 步骤4: 触发网络请求
1. 在闲鱼页面进行操作：
   - 刷新页面 (F5)
   - 点击任意商品
   - 搜索商品
   - 访问个人中心
2. 观察Network标签页出现请求记录

### 步骤5: 复制Cookie
1. 在请求列表中选择任意一个 `xianyu.com` 域名的请求
2. 点击该请求，在右侧查看详情
3. 找到 `Request Headers` (请求标头) 部分
4. 找到 `Cookie:` 行
5. 复制整行Cookie值（不包括"Cookie:"标签）

### 步骤6: 验证Cookie
1. 将Cookie粘贴到配置界面
2. 点击"🔍 验证Cookie"按钮
3. 确认包含所有必要字段

## ⚠️ 常见问题

### Q1: Cookie验证失败，提示缺少unb字段
**原因**: Cookie不完整或未正确复制
**解决**: 
1. 确认已完全登录闲鱼
2. 重新按步骤获取完整Cookie
3. 检查复制时是否遗漏部分内容

### Q2: Cookie很长，是否正常？
**答案**: 是的，完整的Cookie通常很长，包含几十个字段，这是正常的。

### Q3: Cookie多久失效？
**答案**: 通常几天到几周，具体取决于闲鱼的策略。如果程序提示Cookie失效，需要重新获取。

### Q4: 获取Cookie时Network标签页没有请求
**解决**:
1. 确认已切换到Network标签页
2. 刷新闲鱼页面或进行其他操作
3. 检查是否有过滤器阻止显示请求

### Q5: 找不到xianyu.com的请求
**解决**:
1. 在过滤器中输入"xianyu"进行筛选
2. 或选择任意请求，只要Cookie完整即可
3. 确保在闲鱼官网操作，不是其他网站

## 🔒 安全提醒

1. **保密性**: Cookie包含您的登录信息，请勿分享给他人
2. **环境**: 不要在公共场所或不安全的网络环境中操作
3. **更新**: 定期更新Cookie以保持有效性
4. **备份**: 建议保存有效的Cookie作为备份

## 🛠️ 故障排除

如果按照教程仍无法获取有效Cookie：

1. **检查登录状态**: 确保在闲鱼网站已正确登录
2. **清除缓存**: 清除浏览器缓存后重新登录
3. **更换浏览器**: 尝试使用不同的浏览器
4. **检查网络**: 确保网络连接正常
5. **联系支持**: 如果问题持续，请检查程序日志获取更多信息

---

💡 **提示**: 获取Cookie是一次性操作，配置成功后就可以长期使用，直到Cookie失效需要更新。
