# 🤖 DeepSeek V3 配置指南

## 📋 概述

DeepSeek V3 是一个强大的开源大语言模型，具有优秀的中文理解能力和较低的使用成本。本指南将帮助您在XianyuAutoAgent中配置和使用DeepSeek API。

## 🔑 获取API密钥

### 1. 注册DeepSeek账户
1. 访问 [DeepSeek官网](https://www.deepseek.com/)
2. 点击"注册"创建账户
3. 完成邮箱验证

### 2. 申请API密钥
1. 登录后进入控制台
2. 找到"API密钥"或"API Keys"页面
3. 点击"创建新密钥"
4. 复制生成的API密钥（格式类似：`sk-xxxxxxxxxxxxxxxx`）

⚠️ **重要提醒**: 请妥善保管您的API密钥，不要泄露给他人！

## ⚙️ 配置步骤

### 方法1: 使用配置界面（推荐）

1. **启动配置界面**
   ```bash
   python config_gui.py
   ```

2. **快速配置DeepSeek**
   - 在"🤖 AI配置"部分
   - 点击"🤖 DeepSeek"按钮
   - 系统会自动设置：
     - API基础URL: `https://api.deepseek.com`
     - AI模型: `deepseek-chat`

3. **填入API密钥**
   - 在"OpenAI API密钥"框中输入您的DeepSeek API密钥
   - 注意：虽然标签显示"OpenAI"，但DeepSeek API兼容OpenAI格式

4. **选择模型**
   - `deepseek-chat`: DeepSeek-V3-0324，适合日常对话
   - `deepseek-reasoner`: DeepSeek-R1-0528，适合复杂推理

5. **保存配置**
   - 点击"💾 保存配置"
   - 点击"🧪 测试配置"验证设置

### 方法2: 手动编辑.env文件

如果您偏好手动配置，可以直接编辑`.env`文件：

```env
# DeepSeek API配置
API_KEY=sk-your-deepseek-api-key-here
BASE_URL=https://api.deepseek.com
MODEL=deepseek-chat

# 其他AI参数
TEMPERATURE=0.7
MAX_TOKENS=2000

# 闲鱼配置（必需）
COOKIES_STR=your-xianyu-cookies-here
```

## 🎯 模型选择指南

### DeepSeek-V3 (deepseek-chat)
- **适用场景**: 日常客服、商品咨询、价格谈判
- **优势**: 
  - 🌟 中文理解能力强
  - 💰 成本相对较低
  - ⚡ 响应速度快
- **推荐用途**: 闲鱼自动客服的主力模型

### DeepSeek-R1 (deepseek-reasoner)
- **适用场景**: 复杂问题分析、技术咨询
- **优势**:
  - 🧠 推理能力强
  - 🔍 逻辑分析深入
  - 📊 适合复杂决策
- **注意**: 响应时间较长，成本较高

## 💡 配置建议

### 基础配置
```
模型: deepseek-chat
温度: 0.7 (平衡创造性和准确性)
最大令牌: 2000 (足够的回复长度)
```

### 高级配置
- **温度调节**:
  - `0.3-0.5`: 更准确、一致的回复
  - `0.7-0.9`: 更有创造性的回复
  
- **令牌限制**:
  - `1000-1500`: 简短回复
  - `2000-3000`: 详细回复
  - `4000+`: 长篇回复

## 🔧 故障排除

### 常见错误

#### ❌ "API密钥无效"
**解决方案**:
1. 检查API密钥格式是否正确
2. 确认密钥未过期
3. 验证账户状态正常

#### ❌ "连接超时"
**解决方案**:
1. 检查网络连接
2. 确认防火墙设置
3. 尝试更换网络环境

#### ❌ "模型不存在"
**解决方案**:
1. 确认模型名称正确：
   - `deepseek-chat` (不是 `deepseek-v3`)
   - `deepseek-reasoner` (不是 `deepseek-r1`)
2. 检查API文档更新

### 性能优化

#### 提升响应速度
1. 使用`deepseek-chat`而非`deepseek-reasoner`
2. 适当降低`max_tokens`值
3. 优化提示词长度

#### 降低使用成本
1. 合理设置`max_tokens`
2. 优化提示词效率
3. 避免不必要的API调用

## 📊 成本对比

| 模型 | 输入价格 | 输出价格 | 适用场景 |
|------|----------|----------|----------|
| deepseek-chat | 较低 | 较低 | 日常客服 |
| deepseek-reasoner | 中等 | 中等 | 复杂咨询 |
| gpt-3.5-turbo | 低 | 低 | 基础对话 |
| gpt-4o | 高 | 高 | 高质量回复 |

💡 **建议**: 对于闲鱼客服场景，`deepseek-chat`提供了最佳的性价比。

## 🚀 快速开始

1. **获取API密钥** → DeepSeek官网申请
2. **运行配置界面** → `python config_gui.py`
3. **点击DeepSeek按钮** → 自动配置API地址
4. **输入API密钥** → 粘贴您的密钥
5. **测试配置** → 点击"🧪 测试配置"
6. **保存并运行** → 开始使用DeepSeek V3！

## 📞 技术支持

如果遇到配置问题：
1. 查看 [故障排除指南](TROUBLESHOOTING.md)
2. 检查日志文件：`logs/xianyu_agent.log`
3. 参考 [配置界面指南](PYTHON_GUI_GUIDE.md)

---

🎉 **恭喜！** 您已成功配置DeepSeek V3，享受强大的AI客服体验！
