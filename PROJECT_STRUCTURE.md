# 📁 XianyuAutoAgent 项目结构

## 🗂️ 目录结构

```
XianyuAutoAgent-main/
├── 📄 核心程序文件
│   ├── main.py                 # 🚀 主程序入口
│   ├── start.py                # 🎯 一键启动脚本
│   ├── XianyuAgent.py          # 🤖 AI代理核心逻辑
│   ├── XianyuApis.py           # 🔌 闲鱼API接口
│   └── config.py               # ⚙️ 配置管理
│
├── 🖥️ 用户界面
│   ├── config_gui.py           # 🎨 Python桌面配置界面
│   └── check_config.py         # 🔍 配置检查工具
│
├── 🛠️ 核心模块
│   ├── context_manager.py      # 📝 上下文管理
│   ├── exceptions.py           # ⚠️ 异常处理
│   ├── logger_config.py        # 📊 日志配置
│   └── security.py             # 🔒 安全模块
│
├── 📚 文档指南
│   ├── README.md               # 📖 项目说明
│   ├── START_HERE.md           # 🚀 快速开始指南
│   ├── PYTHON_GUI_GUIDE.md     # 🖥️ GUI使用指南
│   ├── COOKIE_EXAMPLE.md       # 🍪 Cookie获取教程
│   ├── UPGRADE_NOTES.md        # 📈 升级说明
│   └── PROJECT_STRUCTURE.md    # 📁 项目结构说明
│
├── 🗃️ 数据目录
│   ├── data/                   # 💾 数据存储目录
│   ├── logs/                   # 📋 日志文件目录
│   └── prompts/                # 💬 AI提示词目录
│       ├── classify_prompt.txt # 🏷️ 分类提示词
│       ├── price_prompt.txt    # 💰 价格咨询提示词
│       ├── tech_prompt.txt     # 🔧 技术咨询提示词
│       └── default_prompt.txt  # 📝 默认提示词
│
├── 🖼️ 资源文件
│   └── images/                 # 🎨 图片资源
│       ├── demo1.png           # 📸 演示图片1
│       ├── demo2.png           # 📸 演示图片2
│       ├── demo3.png           # 📸 演示图片3
│       ├── log.png             # 📊 日志截图
│       ├── wx_group6.png       # 👥 微信群二维码1
│       ├── wx_group7.png       # 👥 微信群二维码2
│       ├── wechat_pay.jpg      # 💳 微信支付码
│       └── alipay.jpg          # 💳 支付宝收款码
│
├── 🔧 工具目录
│   └── utils/                  # 🛠️ 工具函数
│       ├── __init__.py         # 📦 包初始化
│       └── xianyu_utils.py     # 🐟 闲鱼工具函数
│
└── 📋 配置文件
    ├── requirements.txt        # 📦 Python依赖
    ├── LICENSE                 # 📜 开源协议
    └── .gitignore             # 🚫 Git忽略文件
```

## 📄 核心文件说明

### 🚀 启动文件
- **start.py**: 一键启动脚本，提供菜单式操作选择
- **main.py**: 主程序入口，启动AI客服代理

### 🤖 核心逻辑
- **XianyuAgent.py**: AI代理核心，处理消息分类和回复生成
- **XianyuApis.py**: 闲鱼平台API接口封装
- **config.py**: 配置文件管理和环境变量处理

### 🖥️ 用户界面
- **config_gui.py**: Python桌面配置界面（推荐使用）
- **check_config.py**: 配置验证工具

### 🛠️ 支持模块
- **context_manager.py**: 对话上下文管理
- **exceptions.py**: 统一异常处理
- **logger_config.py**: 日志系统配置
- **security.py**: 数据加密和安全功能

## 📚 文档体系

### 📖 用户文档
- **README.md**: 项目总体介绍和功能说明
- **START_HERE.md**: 快速开始指南，包含多种启动方式
- **PYTHON_GUI_GUIDE.md**: 桌面配置界面详细使用指南

### 🔧 技术文档
- **COOKIE_EXAMPLE.md**: Cookie获取详细教程
- **UPGRADE_NOTES.md**: 版本升级记录和说明
- **PROJECT_STRUCTURE.md**: 项目结构说明（本文档）

## 🗃️ 数据管理

### 📁 目录说明
- **data/**: 存储数据库文件、用户数据等
- **logs/**: 存储程序运行日志
- **prompts/**: 存储AI提示词模板

### 🔄 自动管理
- 程序启动时自动创建必要目录
- 配置界面自动管理提示词文件
- 日志系统自动轮转和清理

## 🎨 资源文件

### 📸 演示图片
- **demo1-3.png**: 功能演示截图，用于README展示
- **log.png**: 后台日志截图

### 👥 社区资源
- **wx_group6-7.png**: 微信交流群二维码
- **wechat_pay.jpg / alipay.jpg**: 赞赏支持二维码

## 🔧 开发工具

### 📦 依赖管理
- **requirements.txt**: Python包依赖列表
- 使用 `pip install -r requirements.txt` 安装

### 🛠️ 工具函数
- **utils/**: 通用工具函数库
- **xianyu_utils.py**: 闲鱼平台相关工具

## 🚫 忽略文件

### .gitignore 配置
- 环境变量文件 (.env)
- Python缓存文件 (__pycache__)
- 日志文件 (logs/)
- 数据库文件 (*.db, *.sqlite)
- IDE配置文件

## 🎯 使用建议

### 🚀 快速开始
1. 运行 `python start.py` 获得菜单式操作
2. 首次使用选择"配置界面"进行设置
3. 配置完成后选择"运行主程序"

### 🔧 配置管理
- 优先使用 `config_gui.py` 桌面界面配置
- 使用 `check_config.py` 验证配置正确性
- 参考 `COOKIE_EXAMPLE.md` 获取Cookie

### 📊 监控运维
- 查看 `logs/` 目录了解运行状态
- 使用日志级别控制输出详细程度
- 定期备份 `data/` 目录重要数据

---

💡 **提示**: 项目结构经过优化，删除了冗余文件，保持简洁高效的组织方式。
