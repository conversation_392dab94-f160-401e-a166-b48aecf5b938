# 🖥️ Python桌面配置界面使用指南

## 📖 简介

XianyuAutoAgent现在提供了原生Python桌面配置界面，使用tkinter构建，无需额外依赖，提供更好的集成性和用户体验。

## 🚀 快速启动

```bash
# 1. 安装依赖（如果还没安装）
pip install -r requirements.txt

# 2. 启动桌面配置界面
python config_gui.py
```

## 🎯 Python桌面UI的优势

### 🖥️ 原生体验
- **无需浏览器**: 直接运行在桌面，不依赖Web浏览器
- **系统集成**: 完美融入操作系统，支持系统主题
- **性能优异**: 原生GUI性能更好，响应更快

### 🔧 功能完整
- **所有配置项**: 覆盖API密钥、Cookie、AI设置等所有配置
- **可视化编辑**: 直观的表单界面，支持下拉框、滑块等控件
- **实时验证**: 输入验证和错误提示
- **配置测试**: 内置配置有效性测试

### 🛡️ 安全可靠
- **本地运行**: 所有数据都在本地处理，不会上传到服务器
- **密码保护**: 敏感信息如API密钥自动隐藏显示
- **文件管理**: 自动管理配置文件和提示词文件

## 📋 界面功能详解

### 🔑 基础配置标签页
**API配置**:
- OpenAI API密钥（必填）
- API基础URL（默认OpenAI官方地址）

**闲鱼配置**:
- Cookie字符串（必填，从浏览器获取）
- 支持多行文本输入

**安全配置**:
- 加密密钥（可选）
- 一键生成随机密钥功能

### 🧠 AI设置标签页
**模型配置**:
- AI模型选择（GPT-4o Mini、GPT-4o、GPT-3.5-turbo）
- 下拉框选择，防止输入错误

**参数配置**:
- 创造性温度（0-2，滑块调节）
- 最大令牌数（数字输入框）

### 📝 提示词标签页
**提示词管理**:
- 分类提示词：判断消息类型
- 价格提示词：处理价格咨询
- 技术提示词：处理技术问题
- 默认提示词：处理一般咨询

**编辑功能**:
- 大文本编辑区域
- 提示词类型切换
- 实时保存编辑内容

### ⚙️ 高级设置标签页
**WebSocket配置**:
- WebSocket连接URL
- 超时时间设置

**数据库配置**:
- 数据库文件路径
- 文件浏览器选择

**日志配置**:
- 日志级别选择
- 日志文件路径

## 🔧 操作指南

### 第一次使用
1. **启动界面**: 运行 `python config_gui.py`
2. **填写基础配置**: 在"基础配置"标签页填入API密钥和Cookie
3. **调整AI设置**: 根据需要调整模型和参数
4. **自定义提示词**: 在"提示词"标签页编辑AI回复模板
5. **保存配置**: 点击"💾 保存配置"按钮

### 获取闲鱼Cookie
1. 打开浏览器，登录闲鱼网站 (https://www.xianyu.com)
2. 按F12打开开发者工具
3. 切换到"Network"（网络）标签
4. 刷新页面或进行任何操作
5. 找到任意一个xianyu.com的请求，查看Request Headers
6. 复制完整的Cookie字符串
7. 粘贴到配置界面的Cookie文本框
8. 点击"🔍 验证Cookie"确保包含必要字段(unb, _tb_token_, cookie2)

**重要**: Cookie必须包含`unb`字段，这是用户的唯一标识符。如果缺少此字段，程序无法正常运行。详细获取方法请参考 [Cookie获取说明](COOKIE_EXAMPLE.md)

### 配置测试
- 点击"🧪 测试配置"验证设置是否正确
- 系统会检查必填项和参数范围
- 如有问题会显示具体错误信息

### 其他功能
- **🔄 重新加载**: 从文件重新读取配置
- **📁 打开配置目录**: 快速打开项目目录
- **🔑 生成随机密钥**: 自动生成安全的加密密钥

## 📁 配置文件说明

界面会自动管理以下文件：

### 环境配置
- `.env` - 存储所有环境变量配置

### 提示词文件
- `prompts/classify_prompt.txt` - 分类提示词
- `prompts/price_prompt.txt` - 价格咨询提示词
- `prompts/tech_prompt.txt` - 技术咨询提示词
- `prompts/default_prompt.txt` - 默认提示词

### 自动创建目录
- `data/` - 数据存储目录
- `logs/` - 日志文件目录
- `prompts/` - 提示词文件目录

## 🛠️ 故障排除

### 界面无法启动
1. 确认Python版本（建议3.7+）
2. 检查tkinter是否可用：`python -c "import tkinter"`
3. 查看控制台错误信息

### 配置保存失败
1. 检查文件权限，确保可以写入.env文件
2. 确认prompts目录存在且可写
3. 验证配置格式是否正确

### 提示词编辑问题
1. 确保prompts目录存在
2. 检查文件编码（应为UTF-8）
3. 验证文件权限

## 🎨 界面特色

### 现代化设计
- 使用ttk主题样式
- 清晰的标签页布局
- 直观的图标和标识

### 用户友好
- 滚动区域支持大量内容
- 智能表单验证
- 详细的操作提示

### 高效操作
- 快捷键支持
- 批量配置保存
- 一键测试功能

## 🔄 与主程序集成

配置完成后，直接运行主程序：
```bash
python main.py
```

配置界面保存的设置会自动被主程序读取和使用。

---

🎉 **享受更好的配置体验！** Python桌面界面让XianyuAutoAgent的配置变得简单而高效！
