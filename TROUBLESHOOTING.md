# 🔧 XianyuAutoAgent 故障排除指南

## 🚨 常见错误及解决方案

### 1. Cookie相关错误

#### ❌ "Cookie中缺少必要的unb字段"
**错误原因**: Cookie不完整或格式错误

**解决方案**:
1. 打开配置界面: `python config_gui.py`
2. 点击"📖 获取教程"查看详细步骤
3. 重新从闲鱼网站获取完整Cookie
4. 使用"🔍 验证Cookie"确认包含必要字段

#### ❌ Cookie验证失败
**检查清单**:
- [ ] 已在 https://www.xianyu.com 完全登录
- [ ] 复制了完整的Cookie字符串
- [ ] Cookie包含 `unb`, `_tb_token_`, `cookie2` 字段
- [ ] 没有额外的空格或换行符

### 2. API限流错误

#### ❌ "哎哟喂,被挤爆啦,请稍后重试"
**错误原因**: 触发闲鱼反爬虫机制

**立即处理**:
1. **停止程序** - 立即停止当前运行
2. **等待冷却** - 等待5-10分钟
3. **重新获取Cookie** - 清除浏览器缓存后重新登录
4. **降低频率** - 程序已自动增加请求间隔

**预防措施**:
- 避免频繁启动程序
- 不要同时运行多个实例
- 定期更新Cookie

#### ❌ "FAIL_SYS_USER_VALIDATE"
**错误原因**: 用户验证失败

**解决步骤**:
1. 检查闲鱼账号状态
2. 重新登录闲鱼网站
3. 获取新的Cookie
4. 等待一段时间后重试

### 3. 网络连接错误

#### ❌ 连接超时或网络错误
**检查项目**:
- [ ] 网络连接正常
- [ ] 能正常访问闲鱼网站
- [ ] 防火墙未阻止程序
- [ ] 代理设置正确（如使用）

**解决方案**:
1. 检查网络连接
2. 尝试访问 https://www.xianyu.com
3. 重启网络或更换网络环境
4. 检查防火墙设置

### 4. 配置文件错误

#### ❌ ".env文件不存在"
**解决方案**:
1. 运行配置界面: `python config_gui.py`
2. 填写必要配置并保存
3. 或手动创建.env文件

#### ❌ "API密钥无效"
**检查项目**:
- [ ] API密钥格式正确
- [ ] API密钥未过期
- [ ] 账户余额充足
- [ ] API服务正常

### 5. 提示词文件错误

#### ❌ "提示词文件不存在"
**自动修复**:
程序会自动创建缺失的提示词文件

**手动修复**:
1. 运行配置界面
2. 在"提示词"标签页编辑内容
3. 保存配置

### 6. 权限错误

#### ❌ "无法写入文件"
**解决方案**:
1. 检查文件夹权限
2. 以管理员身份运行
3. 检查磁盘空间
4. 关闭占用文件的程序

## 🛠️ 诊断工具

### 配置检查
```bash
# 检查配置完整性
python check_config.py

# 打开配置界面
python config_gui.py

# 测试配置
# 在配置界面点击"🧪 测试配置"
```

### 日志分析
```bash
# 查看错误日志
type logs\xianyu_agent.error.log

# 查看完整日志
type logs\xianyu_agent.log
```

### 网络测试
```bash
# 测试网络连接
ping www.xianyu.com

# 测试HTTPS连接
curl -I https://www.xianyu.com
```

## 🔍 问题排查流程

### 步骤1: 基础检查
1. 确认Python版本 (建议3.7+)
2. 检查依赖安装: `pip install -r requirements.txt`
3. 验证文件权限

### 步骤2: 配置验证
1. 运行: `python check_config.py`
2. 检查.env文件是否存在
3. 验证API密钥和Cookie

### 步骤3: 网络测试
1. 测试网络连接
2. 访问闲鱼网站确认正常
3. 检查防火墙设置

### 步骤4: 重新配置
1. 清除浏览器缓存
2. 重新登录闲鱼
3. 获取新的Cookie
4. 更新配置文件

### 步骤5: 逐步测试
1. 先测试配置: 配置界面 → "🧪 测试配置"
2. 再运行程序: `python main.py`
3. 观察日志输出

## 📞 获取帮助

### 自助资源
- [快速开始指南](START_HERE.md)
- [配置界面指南](PYTHON_GUI_GUIDE.md)
- [Cookie获取教程](COOKIE_EXAMPLE.md)
- [项目结构说明](PROJECT_STRUCTURE.md)

### 日志信息
遇到问题时，请提供：
1. 错误信息截图
2. 日志文件内容
3. 操作步骤描述
4. 系统环境信息

### 常用命令
```bash
# 一键启动（推荐）
python start.py

# 配置管理
python config_gui.py

# 配置检查
python check_config.py

# 运行主程序
python main.py
```

## 💡 最佳实践

### 使用建议
1. **定期更新Cookie** - 建议每周更新一次
2. **避免频繁重启** - 给服务器足够的冷却时间
3. **监控日志** - 定期查看日志了解运行状态
4. **备份配置** - 保存有效的配置作为备份

### 预防措施
1. **使用配置界面** - 避免手动编辑配置文件
2. **测试后运行** - 配置完成后先测试再运行
3. **单实例运行** - 避免同时运行多个程序实例
4. **网络稳定** - 确保网络环境稳定

---

🎯 **记住**: 大多数问题都可以通过重新获取Cookie和等待冷却时间来解决！
