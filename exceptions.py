"""
自定义异常类和错误处理模块
提供统一的异常处理机制
"""

from typing import Optional, Dict, Any
from loguru import logger
import traceback
import functools
import asyncio


class XianyuAgentException(Exception):
    """闲鱼代理基础异常类"""
    
    def __init__(self, message: str, error_code: str = "UNKNOWN", details: Optional[Dict[str, Any]] = None):
        self.message = message
        self.error_code = error_code
        self.details = details or {}
        super().__init__(self.message)
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            "error_code": self.error_code,
            "message": self.message,
            "details": self.details
        }


class ConfigurationError(XianyuAgentException):
    """配置错误"""
    
    def __init__(self, message: str, config_key: Optional[str] = None):
        super().__init__(
            message=message,
            error_code="CONFIG_ERROR",
            details={"config_key": config_key} if config_key else {}
        )


class AuthenticationError(XianyuAgentException):
    """认证错误"""
    
    def __init__(self, message: str = "认证失败"):
        super().__init__(
            message=message,
            error_code="AUTH_ERROR"
        )


class NetworkError(XianyuAgentException):
    """网络错误"""
    
    def __init__(self, message: str, status_code: Optional[int] = None, url: Optional[str] = None):
        super().__init__(
            message=message,
            error_code="NETWORK_ERROR",
            details={"status_code": status_code, "url": url}
        )


class DatabaseError(XianyuAgentException):
    """数据库错误"""
    
    def __init__(self, message: str, operation: Optional[str] = None):
        super().__init__(
            message=message,
            error_code="DATABASE_ERROR",
            details={"operation": operation} if operation else {}
        )


class AIServiceError(XianyuAgentException):
    """AI服务错误"""
    
    def __init__(self, message: str, model: Optional[str] = None, prompt_length: Optional[int] = None):
        super().__init__(
            message=message,
            error_code="AI_SERVICE_ERROR",
            details={"model": model, "prompt_length": prompt_length}
        )


class WebSocketError(XianyuAgentException):
    """WebSocket连接错误"""
    
    def __init__(self, message: str, connection_state: Optional[str] = None):
        super().__init__(
            message=message,
            error_code="WEBSOCKET_ERROR",
            details={"connection_state": connection_state} if connection_state else {}
        )


class SecurityError(XianyuAgentException):
    """安全相关错误"""
    
    def __init__(self, message: str, security_level: str = "HIGH"):
        super().__init__(
            message=message,
            error_code="SECURITY_ERROR",
            details={"security_level": security_level}
        )


def handle_exceptions(
    default_return=None,
    log_level: str = "ERROR",
    reraise: bool = False,
    exception_types: tuple = (Exception,)
):
    """
    异常处理装饰器
    
    Args:
        default_return: 异常时的默认返回值
        log_level: 日志级别
        reraise: 是否重新抛出异常
        exception_types: 要捕获的异常类型
    """
    def decorator(func):
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            try:
                return func(*args, **kwargs)
            except exception_types as e:
                error_msg = f"函数 {func.__name__} 执行失败: {str(e)}"
                
                # 记录详细的错误信息
                if log_level.upper() == "ERROR":
                    logger.error(error_msg)
                    logger.error(f"错误详情: {traceback.format_exc()}")
                elif log_level.upper() == "WARNING":
                    logger.warning(error_msg)
                elif log_level.upper() == "DEBUG":
                    logger.debug(error_msg)
                
                if reraise:
                    raise
                return default_return
        
        return wrapper
    return decorator


def handle_async_exceptions(
    default_return=None,
    log_level: str = "ERROR",
    reraise: bool = False,
    exception_types: tuple = (Exception,)
):
    """
    异步函数异常处理装饰器
    """
    def decorator(func):
        @functools.wraps(func)
        async def wrapper(*args, **kwargs):
            try:
                return await func(*args, **kwargs)
            except exception_types as e:
                error_msg = f"异步函数 {func.__name__} 执行失败: {str(e)}"
                
                # 记录详细的错误信息
                if log_level.upper() == "ERROR":
                    logger.error(error_msg)
                    logger.error(f"错误详情: {traceback.format_exc()}")
                elif log_level.upper() == "WARNING":
                    logger.warning(error_msg)
                elif log_level.upper() == "DEBUG":
                    logger.debug(error_msg)
                
                if reraise:
                    raise
                return default_return
        
        return wrapper
    return decorator


class ErrorHandler:
    """统一错误处理器"""
    
    @staticmethod
    def handle_config_error(e: Exception, config_key: str = None) -> None:
        """处理配置错误"""
        error_msg = f"配置错误: {str(e)}"
        if config_key:
            error_msg += f" (配置项: {config_key})"
        logger.error(error_msg)
        raise ConfigurationError(error_msg, config_key)
    
    @staticmethod
    def handle_network_error(e: Exception, url: str = None, operation: str = None) -> None:
        """处理网络错误"""
        error_msg = f"网络请求失败: {str(e)}"
        if operation:
            error_msg = f"{operation} - {error_msg}"
        logger.error(error_msg)
        raise NetworkError(error_msg, url=url)
    
    @staticmethod
    def handle_database_error(e: Exception, operation: str = None) -> None:
        """处理数据库错误"""
        error_msg = f"数据库操作失败: {str(e)}"
        if operation:
            error_msg = f"{operation} - {error_msg}"
        logger.error(error_msg)
        raise DatabaseError(error_msg, operation)
    
    @staticmethod
    def handle_ai_service_error(e: Exception, model: str = None) -> None:
        """处理AI服务错误"""
        error_msg = f"AI服务调用失败: {str(e)}"
        if model:
            error_msg += f" (模型: {model})"
        logger.error(error_msg)
        raise AIServiceError(error_msg, model)
    
    @staticmethod
    def handle_websocket_error(e: Exception, state: str = None) -> None:
        """处理WebSocket错误"""
        error_msg = f"WebSocket连接错误: {str(e)}"
        if state:
            error_msg += f" (状态: {state})"
        logger.error(error_msg)
        raise WebSocketError(error_msg, state)
    
    @staticmethod
    def log_and_continue(e: Exception, context: str = "", level: str = "WARNING") -> None:
        """记录错误但继续执行"""
        error_msg = f"{context}: {str(e)}" if context else str(e)
        
        if level.upper() == "ERROR":
            logger.error(error_msg)
        elif level.upper() == "WARNING":
            logger.warning(error_msg)
        elif level.upper() == "DEBUG":
            logger.debug(error_msg)
        else:
            logger.info(error_msg)


# 全局错误处理器实例
error_handler = ErrorHandler()
