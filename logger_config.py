"""
日志配置模块
提供统一的日志配置和管理功能
"""

import sys
import os
from pathlib import Path
from loguru import logger


def setup_logger(
    log_level: str = "INFO",
    log_file_path: str = "logs/xianyu_agent.log",
    log_format: str = None,
    log_rotation: str = "100 MB",
    log_retention: str = "30 days"
):
    """
    配置日志系统

    Args:
        log_level: 日志级别
        log_file_path: 日志文件路径
        log_format: 日志格式
        log_rotation: 日志轮转大小
        log_retention: 日志保留时间
    """
    try:
        # 默认日志格式
        if log_format is None:
            log_format = "<green>{time:YYYY-MM-DD HH:mm:ss.SSS}</green> | <level>{level: <8}</level> | <cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> - <level>{message}</level>"

        # 移除默认的日志处理器
        logger.remove()

        # 确保日志目录存在
        log_dir = Path(log_file_path).parent
        log_dir.mkdir(parents=True, exist_ok=True)

        # 添加控制台输出
        logger.add(
            sys.stdout,
            format=log_format,
            level=log_level,
            colorize=True,
            backtrace=True,
            diagnose=True
        )

        # 添加文件输出
        logger.add(
            log_file_path,
            format=log_format,
            level=log_level,
            rotation=log_rotation,
            retention=log_retention,
            compression="zip",
            backtrace=True,
            diagnose=True,
            encoding="utf-8"
        )

        # 添加错误日志文件
        error_log_path = str(Path(log_file_path).with_suffix('.error.log'))
        logger.add(
            error_log_path,
            format=log_format,
            level="ERROR",
            rotation=log_rotation,
            retention=log_retention,
            compression="zip",
            backtrace=True,
            diagnose=True,
            encoding="utf-8"
        )

        logger.info("日志系统初始化完成")
        logger.info(f"日志级别: {log_level}")
        logger.info(f"日志文件: {log_file_path}")
        logger.info(f"错误日志: {error_log_path}")

    except Exception as e:
        print(f"日志系统初始化失败: {e}")
        raise


def get_logger(name: str = None):
    """获取日志记录器"""
    if name:
        return logger.bind(name=name)
    return logger


def init_logger_with_config():
    """使用配置文件初始化日志系统"""
    try:
        # 延迟导入避免循环依赖
        from config import config
        setup_logger(
            log_level=config.log.level,
            log_file_path=config.log.file_path,
            log_format=config.log.format,
            log_rotation=config.log.rotation,
            log_retention=config.log.retention
        )
    except ImportError:
        # 如果配置模块不可用，使用默认配置
        setup_logger()


# 在模块导入时使用默认配置初始化日志
setup_logger()
