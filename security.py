"""
安全工具模块
提供加密、验证、访问控制等安全功能
"""

import hashlib
import hmac
import secrets
import base64
import json
import time
from typing import Dict, Any, Optional, List
from cryptography.fernet import Fernet
from cryptography.hazmat.primitives import hashes
from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC
from loguru import logger
import re
from exceptions import SecurityError


class CookieEncryption:
    """Cookie加密工具"""
    
    def __init__(self, password: Optional[str] = None):
        """
        初始化加密工具
        
        Args:
            password: 加密密码，如果为None则生成随机密码
        """
        if password is None:
            password = secrets.token_urlsafe(32)
        
        self.password = password.encode()
        self._fernet = self._create_fernet()
    
    def _create_fernet(self) -> Fernet:
        """创建Fernet加密实例"""
        salt = b'xianyu_agent_salt'  # 在生产环境中应该使用随机salt
        kdf = PBKDF2HMAC(
            algorithm=hashes.SHA256(),
            length=32,
            salt=salt,
            iterations=100000,
        )
        key = base64.urlsafe_b64encode(kdf.derive(self.password))
        return Fernet(key)
    
    def encrypt_cookies(self, cookies_str: str) -> str:
        """加密Cookie字符串"""
        try:
            encrypted_data = self._fernet.encrypt(cookies_str.encode())
            return base64.urlsafe_b64encode(encrypted_data).decode()
        except Exception as e:
            logger.error(f"Cookie加密失败: {e}")
            raise SecurityError(f"Cookie加密失败: {e}")
    
    def decrypt_cookies(self, encrypted_cookies: str) -> str:
        """解密Cookie字符串"""
        try:
            encrypted_data = base64.urlsafe_b64decode(encrypted_cookies.encode())
            decrypted_data = self._fernet.decrypt(encrypted_data)
            return decrypted_data.decode()
        except Exception as e:
            logger.error(f"Cookie解密失败: {e}")
            raise SecurityError(f"Cookie解密失败: {e}")


class InputValidator:
    """输入验证器"""
    
    @staticmethod
    def validate_user_id(user_id: str) -> bool:
        """验证用户ID格式"""
        if not user_id or not isinstance(user_id, str):
            return False
        # 用户ID应该是数字字符串
        return user_id.isdigit() and len(user_id) <= 20
    
    @staticmethod
    def validate_item_id(item_id: str) -> bool:
        """验证商品ID格式"""
        if not item_id or not isinstance(item_id, str):
            return False
        # 商品ID应该是数字字符串
        return item_id.isdigit() and len(item_id) <= 20
    
    @staticmethod
    def validate_chat_id(chat_id: str) -> bool:
        """验证会话ID格式"""
        if not chat_id or not isinstance(chat_id, str):
            return False
        # 会话ID可以包含字母数字和下划线
        return re.match(r'^[a-zA-Z0-9_-]+$', chat_id) and len(chat_id) <= 50
    
    @staticmethod
    def sanitize_message(message: str) -> str:
        """清理用户消息"""
        if not isinstance(message, str):
            return ""
        
        # 移除潜在的恶意字符
        sanitized = re.sub(r'[<>"\']', '', message)
        # 限制长度
        sanitized = sanitized[:1000]
        return sanitized.strip()
    
    @staticmethod
    def validate_cookies(cookies_str: str) -> bool:
        """验证Cookie格式"""
        if not cookies_str or not isinstance(cookies_str, str):
            return False
        
        # 检查是否包含必要的Cookie字段
        required_fields = ['unb', '_m_h5_tk']
        for field in required_fields:
            if field not in cookies_str:
                return False
        
        return True
    
    @staticmethod
    def validate_api_key(api_key: str) -> bool:
        """验证API密钥格式"""
        if not api_key or not isinstance(api_key, str):
            return False
        
        # API密钥应该有一定长度且不包含空格
        return len(api_key) >= 10 and ' ' not in api_key


class AccessController:
    """访问控制器"""
    
    def __init__(self):
        self.failed_attempts: Dict[str, List[float]] = {}
        self.blocked_ips: Dict[str, float] = {}
        self.session_tokens: Dict[str, Dict[str, Any]] = {}
    
    def check_rate_limit(self, identifier: str, max_attempts: int = 5, window_seconds: int = 300) -> bool:
        """检查访问频率限制"""
        current_time = time.time()
        
        # 清理过期的尝试记录
        if identifier in self.failed_attempts:
            self.failed_attempts[identifier] = [
                attempt_time for attempt_time in self.failed_attempts[identifier]
                if current_time - attempt_time < window_seconds
            ]
        
        # 检查是否超过限制
        attempts = len(self.failed_attempts.get(identifier, []))
        return attempts < max_attempts
    
    def record_failed_attempt(self, identifier: str) -> None:
        """记录失败尝试"""
        current_time = time.time()
        if identifier not in self.failed_attempts:
            self.failed_attempts[identifier] = []
        self.failed_attempts[identifier].append(current_time)
        logger.warning(f"记录失败尝试: {identifier}")
    
    def is_blocked(self, identifier: str, block_duration: int = 3600) -> bool:
        """检查是否被阻止"""
        if identifier in self.blocked_ips:
            if time.time() - self.blocked_ips[identifier] < block_duration:
                return True
            else:
                # 解除阻止
                del self.blocked_ips[identifier]
        return False
    
    def block_identifier(self, identifier: str) -> None:
        """阻止标识符"""
        self.blocked_ips[identifier] = time.time()
        logger.warning(f"阻止访问: {identifier}")
    
    def create_session_token(self, user_id: str, expires_in: int = 7200) -> str:
        """创建会话令牌"""
        token = secrets.token_urlsafe(32)
        expires_at = time.time() + expires_in
        
        self.session_tokens[token] = {
            "user_id": user_id,
            "created_at": time.time(),
            "expires_at": expires_at
        }
        
        return token
    
    def validate_session_token(self, token: str) -> Optional[str]:
        """验证会话令牌"""
        if token not in self.session_tokens:
            return None
        
        session = self.session_tokens[token]
        if time.time() > session["expires_at"]:
            # 令牌已过期
            del self.session_tokens[token]
            return None
        
        return session["user_id"]
    
    def revoke_session_token(self, token: str) -> None:
        """撤销会话令牌"""
        if token in self.session_tokens:
            del self.session_tokens[token]


class AntiReplayAttack:
    """防重放攻击"""
    
    def __init__(self, window_seconds: int = 300):
        self.window_seconds = window_seconds
        self.used_nonces: Dict[str, float] = {}
    
    def generate_nonce(self) -> str:
        """生成随机数"""
        return secrets.token_urlsafe(16)
    
    def validate_nonce(self, nonce: str, timestamp: float) -> bool:
        """验证随机数"""
        current_time = time.time()
        
        # 清理过期的nonce
        expired_nonces = [
            n for n, t in self.used_nonces.items()
            if current_time - t > self.window_seconds
        ]
        for n in expired_nonces:
            del self.used_nonces[n]
        
        # 检查时间戳是否在有效窗口内
        if abs(current_time - timestamp) > self.window_seconds:
            return False
        
        # 检查nonce是否已使用
        if nonce in self.used_nonces:
            return False
        
        # 记录nonce
        self.used_nonces[nonce] = timestamp
        return True


class SecurityManager:
    """安全管理器"""
    
    def __init__(self, encryption_password: Optional[str] = None):
        self.cookie_encryption = CookieEncryption(encryption_password)
        self.input_validator = InputValidator()
        self.access_controller = AccessController()
        self.anti_replay = AntiReplayAttack()
    
    def secure_store_cookies(self, cookies_str: str) -> str:
        """安全存储Cookie"""
        if not self.input_validator.validate_cookies(cookies_str):
            raise SecurityError("无效的Cookie格式")
        
        return self.cookie_encryption.encrypt_cookies(cookies_str)
    
    def secure_load_cookies(self, encrypted_cookies: str) -> str:
        """安全加载Cookie"""
        return self.cookie_encryption.decrypt_cookies(encrypted_cookies)
    
    def validate_and_sanitize_input(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """验证和清理输入数据"""
        sanitized_data = {}
        
        for key, value in data.items():
            if key == "user_id" and isinstance(value, str):
                if self.input_validator.validate_user_id(value):
                    sanitized_data[key] = value
                else:
                    raise SecurityError(f"无效的用户ID: {value}")
            
            elif key == "item_id" and isinstance(value, str):
                if self.input_validator.validate_item_id(value):
                    sanitized_data[key] = value
                else:
                    raise SecurityError(f"无效的商品ID: {value}")
            
            elif key == "chat_id" and isinstance(value, str):
                if self.input_validator.validate_chat_id(value):
                    sanitized_data[key] = value
                else:
                    raise SecurityError(f"无效的会话ID: {value}")
            
            elif key == "message" and isinstance(value, str):
                sanitized_data[key] = self.input_validator.sanitize_message(value)
            
            else:
                sanitized_data[key] = value
        
        return sanitized_data


# 全局安全管理器实例
security_manager = SecurityManager()
