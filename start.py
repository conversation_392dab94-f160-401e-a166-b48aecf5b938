#!/usr/bin/env python3
"""
XianyuAutoAgent 快速启动脚本
"""

import os
import sys
import subprocess
from pathlib import Path

def check_dependencies():
    """检查依赖"""
    print("🔍 检查依赖...")
    
    try:
        import tkinter
        print("✅ tkinter 可用")
    except ImportError:
        print("❌ tkinter 不可用，请安装Python的tkinter模块")
        return False
    
    try:
        from dotenv import load_dotenv
        print("✅ python-dotenv 可用")
    except ImportError:
        print("❌ python-dotenv 未安装")
        return False
    
    return True

def check_config():
    """检查配置文件"""
    print("\n📋 检查配置...")
    
    env_file = Path(".env")
    if not env_file.exists():
        print("⚠️ .env 文件不存在，需要配置")
        return False
    
    # 检查提示词文件
    prompt_files = [
        "prompts/classify_prompt.txt",
        "prompts/price_prompt.txt", 
        "prompts/tech_prompt.txt",
        "prompts/default_prompt.txt"
    ]
    
    missing_prompts = []
    for prompt_file in prompt_files:
        if not Path(prompt_file).exists():
            missing_prompts.append(prompt_file)
    
    if missing_prompts:
        print(f"⚠️ 缺少提示词文件: {', '.join(missing_prompts)}")
        return False
    
    print("✅ 配置文件检查通过")
    return True

def main():
    """主函数"""
    print("🤖 XianyuAutoAgent 启动器")
    print("=" * 40)
    
    # 检查依赖
    if not check_dependencies():
        print("\n❌ 依赖检查失败，请先安装依赖:")
        print("   pip install -r requirements.txt")
        return 1
    
    # 检查配置
    config_ok = check_config()
    
    if not config_ok:
        print("\n🔧 需要配置，启动配置界面...")
        try:
            subprocess.run([sys.executable, "config_gui.py"], check=True)
            print("\n✅ 配置完成！")
        except subprocess.CalledProcessError:
            print("\n❌ 配置界面启动失败")
            return 1
        except KeyboardInterrupt:
            print("\n⚠️ 用户取消配置")
            return 0
    
    # 询问用户要执行的操作
    print("\n🚀 选择操作:")
    print("1. 🔧 打开配置界面")
    print("2. ▶️ 运行主程序")
    print("3. 🧪 测试配置")
    print("4. 📖 查看帮助")
    print("0. 🚪 退出")
    
    while True:
        try:
            choice = input("\n请选择 (0-4): ").strip()
            
            if choice == "0":
                print("👋 再见！")
                return 0
            elif choice == "1":
                print("🔧 启动配置界面...")
                subprocess.run([sys.executable, "config_gui.py"])
            elif choice == "2":
                print("▶️ 启动主程序...")
                subprocess.run([sys.executable, "main.py"])
            elif choice == "3":
                print("🧪 运行配置检查...")
                subprocess.run([sys.executable, "check_config.py"])
            elif choice == "4":
                print("\n📖 帮助信息:")
                print("• config_gui.py - 打开配置界面")
                print("• main.py - 运行主程序")
                print("• check_config.py - 检查配置")
                print("• START_HERE.md - 详细使用说明")
                print("• PYTHON_GUI_GUIDE.md - GUI使用指南")
                print("• COOKIE_EXAMPLE.md - Cookie获取教程")
            else:
                print("❌ 无效选择，请输入 0-4")
                continue
                
        except KeyboardInterrupt:
            print("\n👋 再见！")
            return 0
        except Exception as e:
            print(f"❌ 执行失败: {e}")

if __name__ == "__main__":
    sys.exit(main())
