#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
DeepSeek API 测试脚本
用于验证DeepSeek配置是否正确
"""

import os
import sys
from pathlib import Path
from dotenv import load_dotenv
import requests
import json

def test_deepseek_api():
    """测试DeepSeek API连接"""
    print("🤖 DeepSeek API 测试工具")
    print("=" * 50)
    
    # 加载环境变量
    load_dotenv()
    
    # 获取配置
    api_key = os.getenv("API_KEY", "")
    base_url = os.getenv("BASE_URL", "https://api.deepseek.com")
    model = os.getenv("MODEL", "deepseek-chat")
    
    print(f"📍 API地址: {base_url}")
    print(f"🤖 模型: {model}")
    print(f"🔑 API密钥: {'已配置' if api_key else '未配置'}")
    print()
    
    # 检查配置
    if not api_key:
        print("❌ 错误: API_KEY未配置")
        print("请在.env文件中设置API_KEY，或使用配置界面进行配置")
        return False
    
    if not api_key.startswith('sk-'):
        print("⚠️  警告: API密钥格式可能不正确")
        print("DeepSeek API密钥通常以'sk-'开头")
    
    # 构建API请求
    url = f"{base_url.rstrip('/')}/chat/completions"
    headers = {
        "Authorization": f"Bearer {api_key}",
        "Content-Type": "application/json"
    }
    
    data = {
        "model": model,
        "messages": [
            {
                "role": "user", 
                "content": "你好，请简单介绍一下你自己。"
            }
        ],
        "max_tokens": 100,
        "temperature": 0.7
    }
    
    print("🔄 正在测试API连接...")
    
    try:
        response = requests.post(url, headers=headers, json=data, timeout=30)
        
        if response.status_code == 200:
            result = response.json()
            
            if 'choices' in result and len(result['choices']) > 0:
                reply = result['choices'][0]['message']['content']
                print("✅ API测试成功！")
                print(f"🤖 AI回复: {reply}")
                
                # 显示使用统计
                if 'usage' in result:
                    usage = result['usage']
                    print(f"📊 令牌使用: 输入{usage.get('prompt_tokens', 0)}, 输出{usage.get('completion_tokens', 0)}")
                
                return True
            else:
                print("❌ API返回格式异常")
                print(f"响应内容: {result}")
                return False
                
        else:
            print(f"❌ API请求失败 (状态码: {response.status_code})")
            try:
                error_info = response.json()
                print(f"错误信息: {error_info}")
            except:
                print(f"错误信息: {response.text}")
            return False
            
    except requests.exceptions.Timeout:
        print("❌ 请求超时")
        print("可能的原因: 网络连接问题或API服务响应慢")
        return False
        
    except requests.exceptions.ConnectionError:
        print("❌ 连接错误")
        print("可能的原因: 网络连接问题或API地址错误")
        return False
        
    except Exception as e:
        print(f"❌ 未知错误: {str(e)}")
        return False

def show_config_guide():
    """显示配置指南"""
    print("\n📖 配置指南:")
    print("1. 获取DeepSeek API密钥:")
    print("   - 访问 https://www.deepseek.com/")
    print("   - 注册账户并申请API密钥")
    print()
    print("2. 配置方法:")
    print("   方法1: 使用配置界面 (推荐)")
    print("   python config_gui.py")
    print()
    print("   方法2: 手动编辑.env文件")
    print("   API_KEY=sk-your-deepseek-api-key")
    print("   BASE_URL=https://api.deepseek.com")
    print("   MODEL=deepseek-chat")
    print()
    print("3. 详细文档:")
    print("   - DeepSeek配置指南: DEEPSEEK_GUIDE.md")
    print("   - 快速开始指南: START_HERE.md")

def main():
    """主函数"""
    print("🚀 XianyuAutoAgent - DeepSeek API 测试")
    print()
    
    # 检查.env文件
    if not Path(".env").exists():
        print("⚠️  .env文件不存在")
        print("建议使用配置界面创建配置: python config_gui.py")
        show_config_guide()
        return
    
    # 测试API
    success = test_deepseek_api()
    
    if success:
        print("\n🎉 恭喜！DeepSeek配置测试成功")
        print("现在可以运行主程序: python main.py")
    else:
        print("\n❌ 配置测试失败")
        show_config_guide()
    
    print("\n" + "=" * 50)

if __name__ == "__main__":
    main()
