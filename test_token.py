#!/usr/bin/env python3
"""
测试token获取功能的脚本
"""

import os
import sys
from dotenv import load_dotenv
from loguru import logger
from XianyuApis import XianyuApis
from utils.xianyu_utils import generate_device_id

def test_token_retrieval():
    """测试token获取功能"""
    
    # 加载环境变量
    load_dotenv()
    
    # 配置日志
    logger.remove()
    logger.add(
        sys.stderr,
        format="<green>{time:YYYY-MM-DD HH:mm:ss.SSS}</green> | <level>{level: <8}</level> | <cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> - <level>{message}</level>",
        level="INFO"
    )
    
    try:
        # 初始化API客户端
        logger.info("初始化XianyuApis客户端...")
        xianyu_api = XianyuApis()
        
        # 生成设备ID（使用固定用户ID进行测试）
        test_user_id = "test_user_123"
        device_id = generate_device_id(test_user_id)
        logger.info(f"生成的设备ID: {device_id}")
        
        # 尝试获取token
        logger.info("开始获取token...")
        token_result = xianyu_api.get_token(device_id)
        
        if token_result and isinstance(token_result, dict):
            if 'data' in token_result and 'accessToken' in token_result['data']:
                logger.success(f"✅ Token获取成功!")
                logger.info(f"Access Token: {token_result['data']['accessToken'][:20]}...")
                return True
            else:
                logger.warning(f"⚠️ Token获取返回了数据，但格式不符合预期: {token_result}")
                return False
        else:
            logger.error("❌ Token获取失败")
            return False
            
    except KeyboardInterrupt:
        logger.info("用户中断测试")
        return False
    except Exception as e:
        logger.error(f"测试过程中发生异常: {str(e)}")
        return False

if __name__ == "__main__":
    logger.info("🚀 开始测试token获取功能...")
    success = test_token_retrieval()
    
    if success:
        logger.success("🎉 测试完成，token获取成功!")
        sys.exit(0)
    else:
        logger.error("💥 测试失败，请检查配置和网络连接")
        sys.exit(1)
